// 会话管理模块
import { computed } from 'vue'
import type { Ref } from 'vue'
import { apiClient } from '../../api'
import type { ChatSession, Message } from './types'
import type { User } from '../../api'

export class SessionManager {
  private chatSessions: Ref<Map<string, ChatSession>>
  private currentChatUserId: Ref<string>
  private messages: Ref<Map<string, Message[]>>
  private contactsInfo: Map<string, User> = new Map() // 存储联系人信息

  constructor(
    chatSessions: Ref<Map<string, ChatSession>>,
    currentChatUserId: Ref<string>,
    messages: Ref<Map<string, Message[]>>
  ) {
    this.chatSessions = chatSessions
    this.currentChatUserId = currentChatUserId
    this.messages = messages
  }

  // 计算属性：按时间排序的聊天会话
  get sortedChatSessions() {
    return computed(() => {
      return Array.from(this.chatSessions.value.values()).sort(
        (a, b) => b.lastActiveTime - a.lastActiveTime
      )
    })
  }

  // 计算属性：总未读消息数
  get totalUnreadCount() {
    return computed(() => {
      return Array.from(this.chatSessions.value.values()).reduce(
        (total, session) => total + session.unreadCount,
        0
      )
    })
  }

  // 设置联系人信息
  setContactsInfo(contacts: Array<{ id: string; user: User }>) {
    this.contactsInfo.clear()
    contacts.forEach((contact) => {
      this.contactsInfo.set(contact.id, contact.user)
    })
  }

  // 获取联系人信息
  private getContactInfo(userId: string): User | null {
    return this.contactsInfo.get(userId) || null
  }

  // 更新聊天会话
  async updateChatSession(userId: string, lastMessage: Message, getCurrentUserId: () => string) {
    let session = this.chatSessions.value.get(userId)

    if (!session) {
      // 创建新的聊天会话，优先使用已有的联系人信息
      const contactInfo = this.getContactInfo(userId)

      if (contactInfo) {
        session = {
          userId,
          userName: contactInfo.displayName,
          userAvatar: contactInfo.avatar,
          lastMessage,
          unreadCount: 0,
          lastActiveTime: lastMessage.timestamp
        }
      } else {
        // 如果没有联系人信息，尝试从API获取（兜底）
        try {
          const userDetail = await apiClient.getUserDetail(userId)
          if (userDetail.success) {
            session = {
              userId,
              userName: userDetail.user.displayName,
              userAvatar: userDetail.user.avatar,
              lastMessage,
              unreadCount: 0,
              lastActiveTime: lastMessage.timestamp
            }
          } else {
            // 如果获取用户信息失败，使用默认信息
            session = {
              userId,
              userName: `用户${userId}`,
              userAvatar: '/avatars/default.png',
              lastMessage,
              unreadCount: 0,
              lastActiveTime: lastMessage.timestamp
            }
          }
        } catch (err) {
          console.error('获取用户信息失败:', err)
          session = {
            userId,
            userName: `用户${userId}`,
            userAvatar: '/avatars/default.png',
            lastMessage,
            unreadCount: 0,
            lastActiveTime: lastMessage.timestamp
          }
        }
      }
    } else {
      // 更新现有会话
      session.lastMessage = lastMessage
      session.lastActiveTime = lastMessage.timestamp
    }

    // 如果不是当前聊天用户且消息不是自己发送的，增加未读计数
    const currentUserId = getCurrentUserId()
    const isCurrentChatUser = userId === this.currentChatUserId.value
    const isOwnMessage = lastMessage.senderId === currentUserId

    console.log('🔍 [SessionManager] 未读计数判断:', {
      userId,
      currentUserId,
      senderId: lastMessage.senderId,
      currentChatUserId: this.currentChatUserId.value,
      isCurrentChatUser,
      isOwnMessage,
      shouldIncrement: !isCurrentChatUser && !isOwnMessage,
      currentUnreadCount: session.unreadCount
    })

    if (!isCurrentChatUser && !isOwnMessage) {
      session.unreadCount++
      console.log('🔍 [SessionManager] ✅ 未读计数增加:', {
        userId,
        newUnreadCount: session.unreadCount
      })
    } else {
      console.log('🔍 [SessionManager] ❌ 未读计数未增加:', {
        reason: isCurrentChatUser ? '是当前聊天用户' : '是自己发送的消息'
      })
    }

    this.chatSessions.value.set(userId, session)
  }

  // 设置当前聊天用户
  setCurrentChatUser(userId: string, getCurrentUserId: () => string) {
    this.currentChatUserId.value = userId

    // 清除该用户的未读计数
    const session = this.chatSessions.value.get(userId)
    if (session) {
      session.unreadCount = 0
      this.chatSessions.value.set(userId, session)
    }

    // 标记该用户的所有消息为已读
    const userMessages = this.messages.value.get(userId)
    if (userMessages) {
      userMessages.forEach((msg) => {
        if (msg.senderId !== getCurrentUserId()) {
          msg.isRead = true
        }
      })
    }
  }

  // 批量设置聊天会话（用于从API加载的联系人数据）
  setChatSessions(sessions: ChatSession[]) {
    sessions.forEach((session) => {
      this.chatSessions.value.set(session.userId, session)
    })
  }

  // 创建空的聊天会话（用于没有消息历史的用户）
  async createEmptySession(userId: string) {
    // 优先使用已有的联系人信息
    const contactInfo = this.getContactInfo(userId)

    if (contactInfo) {
      // 使用已有的联系人信息
      console.log(`🔍 [SessionManager] 使用已有联系人信息创建空会话:`, contactInfo.displayName)
      const emptySession: ChatSession = {
        userId,
        userName: contactInfo.displayName,
        userAvatar: contactInfo.avatar,
        lastMessage: undefined,
        unreadCount: 0,
        lastActiveTime: Date.now()
      }
      this.chatSessions.value.set(userId, emptySession)
      return emptySession
    } else {
      // 如果没有联系人信息，尝试从API获取（兜底方案）
      console.log(`🔍 [SessionManager] 联系人信息不存在，尝试从API获取用户详情`)
      try {
        const userDetail = await apiClient.getUserDetail(userId)
        if (userDetail.success) {
          const emptySession: ChatSession = {
            userId,
            userName: userDetail.user.displayName,
            userAvatar: userDetail.user.avatar,
            lastMessage: undefined,
            unreadCount: 0,
            lastActiveTime: Date.now()
          }
          this.chatSessions.value.set(userId, emptySession)
          return emptySession
        } else {
          console.error('获取用户信息失败:', userDetail.message)
          return null
        }
      } catch (err) {
        console.error('创建空聊天会话失败:', err)
        return null
      }
    }
  }

  // 获取会话信息
  getSession(userId: string): ChatSession | undefined {
    return this.chatSessions.value.get(userId)
  }

  // 检查是否存在会话
  hasSession(userId: string): boolean {
    return this.chatSessions.value.has(userId)
  }

  // 删除会话
  removeSession(userId: string) {
    this.chatSessions.value.delete(userId)
  }

  // 清空所有会话
  clearAllSessions() {
    this.chatSessions.value.clear()
  }

  // 获取会话数量
  getSessionCount(): number {
    return this.chatSessions.value.size
  }
}
