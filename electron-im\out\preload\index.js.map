{"version": 3, "file": "index.js", "sources": ["../../node_modules/.pnpm/@electron-toolkit+preload@3.0.2_electron@37.2.6/node_modules/@electron-toolkit/preload/dist/index.mjs", "../../src/preload/index.ts"], "sourcesContent": ["import { webUtils, web<PERSON>rame, ipcRenderer, contextBridge } from 'electron';\n\nconst electronAPI = {\n    ipcRenderer: {\n        send(channel, ...args) {\n            ipcRenderer.send(channel, ...args);\n        },\n        sendTo(webContentsId, channel, ...args) {\n            const electronVer = process.versions.electron;\n            const electronMajorVer = electronVer ? parseInt(electronVer.split('.')[0]) : 0;\n            if (electronMajorVer >= 28) {\n                throw new Error('\"sendTo\" method has been removed since Electron 28.');\n            }\n            else {\n                ipcRenderer.sendTo(webContentsId, channel, ...args);\n            }\n        },\n        sendSync(channel, ...args) {\n            return ipcRenderer.sendSync(channel, ...args);\n        },\n        sendToHost(channel, ...args) {\n            ipcRenderer.sendToHost(channel, ...args);\n        },\n        postMessage(channel, message, transfer) {\n            ipcRenderer.postMessage(channel, message, transfer);\n        },\n        invoke(channel, ...args) {\n            return ipcRenderer.invoke(channel, ...args);\n        },\n        on(channel, listener) {\n            ipcRenderer.on(channel, listener);\n            return () => {\n                ipcRenderer.removeListener(channel, listener);\n            };\n        },\n        once(channel, listener) {\n            ipcRenderer.once(channel, listener);\n            return () => {\n                ipcRenderer.removeListener(channel, listener);\n            };\n        },\n        removeListener(channel, listener) {\n            ipcRenderer.removeListener(channel, listener);\n            return this;\n        },\n        removeAllListeners(channel) {\n            ipcRenderer.removeAllListeners(channel);\n        }\n    },\n    webFrame: {\n        insertCSS(css) {\n            return webFrame.insertCSS(css);\n        },\n        setZoomFactor(factor) {\n            if (typeof factor === 'number' && factor > 0) {\n                webFrame.setZoomFactor(factor);\n            }\n        },\n        setZoomLevel(level) {\n            if (typeof level === 'number') {\n                webFrame.setZoomLevel(level);\n            }\n        }\n    },\n    webUtils: {\n        getPathForFile(file) {\n            return webUtils.getPathForFile(file);\n        }\n    },\n    process: {\n        get platform() {\n            return process.platform;\n        },\n        get versions() {\n            return process.versions;\n        },\n        get env() {\n            return { ...process.env };\n        }\n    }\n};\n/**\n * Expose Electron APIs from your preload script, the API\n * will be accessible from the website on `window.electron`.\n */\nfunction exposeElectronAPI() {\n    if (process.contextIsolated) {\n        try {\n            contextBridge.exposeInMainWorld('electron', electronAPI);\n        }\n        catch (error) {\n            console.error(error);\n        }\n    }\n    else {\n        // @ts-ignore (need dts)\n        window.electron = electronAPI;\n    }\n}\n\nexport { electronAPI, exposeElectronAPI };\n", "import { contextBridge } from 'electron'\nimport { electronAPI } from '@electron-toolkit/preload'\n\n// Custom APIs for renderer\nconst api = {}\n\n// Use `contextBridge` APIs to expose Electron APIs to\n// renderer only if context isolation is enabled, otherwise\n// just add to the DOM global.\nif (process.contextIsolated) {\n  try {\n    contextBridge.exposeInMainWorld('electron', electronAPI)\n    contextBridge.exposeInMainWorld('api', api)\n  } catch (error) {\n    console.error(error)\n  }\n} else {\n  // @ts-ignore (define in dts)\n  window.electron = electronAPI\n  // @ts-ignore (define in dts)\n  window.api = api\n}\n"], "names": ["ip<PERSON><PERSON><PERSON><PERSON>", "webFrame", "webUtils", "contextBridge"], "mappings": ";;AAEA,MAAM,cAAc;AAAA,EAChB,aAAa;AAAA,IACT,KAAK,YAAY,MAAM;AACnBA,eAAAA,YAAY,KAAK,SAAS,GAAG,IAAI;AAAA,IACrC;AAAA,IACA,OAAO,eAAe,YAAY,MAAM;AACpC,YAAM,cAAc,QAAQ,SAAS;AACrC,YAAM,mBAAmB,cAAc,SAAS,YAAY,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI;AAC7E,UAAI,oBAAoB,IAAI;AACxB,cAAM,IAAI,MAAM,qDAAqD;AAAA,MACzE,OACK;AACDA,iBAAAA,YAAY,OAAO,eAAe,SAAS,GAAG,IAAI;AAAA,MACtD;AAAA,IACJ;AAAA,IACA,SAAS,YAAY,MAAM;AACvB,aAAOA,qBAAY,SAAS,SAAS,GAAG,IAAI;AAAA,IAChD;AAAA,IACA,WAAW,YAAY,MAAM;AACzBA,eAAAA,YAAY,WAAW,SAAS,GAAG,IAAI;AAAA,IAC3C;AAAA,IACA,YAAY,SAAS,SAAS,UAAU;AACpCA,eAAAA,YAAY,YAAY,SAAS,SAAS,QAAQ;AAAA,IACtD;AAAA,IACA,OAAO,YAAY,MAAM;AACrB,aAAOA,qBAAY,OAAO,SAAS,GAAG,IAAI;AAAA,IAC9C;AAAA,IACA,GAAG,SAAS,UAAU;AAClBA,2BAAY,GAAG,SAAS,QAAQ;AAChC,aAAO,MAAM;AACTA,6BAAY,eAAe,SAAS,QAAQ;AAAA,MAChD;AAAA,IACJ;AAAA,IACA,KAAK,SAAS,UAAU;AACpBA,2BAAY,KAAK,SAAS,QAAQ;AAClC,aAAO,MAAM;AACTA,6BAAY,eAAe,SAAS,QAAQ;AAAA,MAChD;AAAA,IACJ;AAAA,IACA,eAAe,SAAS,UAAU;AAC9BA,2BAAY,eAAe,SAAS,QAAQ;AAC5C,aAAO;AAAA,IACX;AAAA,IACA,mBAAmB,SAAS;AACxBA,eAAAA,YAAY,mBAAmB,OAAO;AAAA,IAC1C;AAAA,EAAA;AAAA,EAEJ,UAAU;AAAA,IACN,UAAU,KAAK;AACX,aAAOC,SAAAA,SAAS,UAAU,GAAG;AAAA,IACjC;AAAA,IACA,cAAc,QAAQ;AAClB,UAAI,OAAO,WAAW,YAAY,SAAS,GAAG;AAC1CA,iBAAAA,SAAS,cAAc,MAAM;AAAA,MACjC;AAAA,IACJ;AAAA,IACA,aAAa,OAAO;AAChB,UAAI,OAAO,UAAU,UAAU;AAC3BA,iBAAAA,SAAS,aAAa,KAAK;AAAA,MAC/B;AAAA,IACJ;AAAA,EAAA;AAAA,EAEJ,UAAU;AAAA,IACN,eAAe,MAAM;AACjB,aAAOC,SAAAA,SAAS,eAAe,IAAI;AAAA,IACvC;AAAA,EAAA;AAAA,EAEJ,SAAS;AAAA,IACL,IAAI,WAAW;AACX,aAAO,QAAQ;AAAA,IACnB;AAAA,IACA,IAAI,WAAW;AACX,aAAO,QAAQ;AAAA,IACnB;AAAA,IACA,IAAI,MAAM;AACN,aAAO,EAAE,GAAG,QAAA,IAAA;AAAA,IAChB;AAAA,EAAA;AAER;AC5EA,MAAM,MAAM,CAAA;AAKZ,IAAI,QAAQ,iBAAiB;AAC3B,MAAI;AACFC,2BAAc,kBAAkB,YAAY,WAAW;AACvDA,2BAAc,kBAAkB,OAAO,GAAG;AAAA,EAC5C,SAAS,OAAO;AACd,YAAQ,MAAM,KAAK;AAAA,EACrB;AACF,OAAO;AAEL,SAAO,WAAW;AAElB,SAAO,MAAM;AACf;", "x_google_ignoreList": [0]}