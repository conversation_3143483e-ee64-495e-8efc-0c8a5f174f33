{"version": 3, "file": "index.js", "sources": ["../../resources/icon.png?asset", "../../src/main/index.ts"], "sourcesContent": ["\n          import { join } from 'path'\n          export default join(__dirname, __VITE_NODE_PUBLIC_ASSET__1ccee481__)", "import { app, shell, BrowserWindow, ipcMain } from 'electron'\nimport { join } from 'path'\nimport { electronApp, optimizer, is } from '@electron-toolkit/utils'\nimport icon from '../../resources/icon.png?asset'\n\nfunction createWindow(): void {\n  // Create the browser window.\n  const mainWindow = new BrowserWindow({\n    width: 1000,\n    height: 700,\n    minWidth: 800, // 设置最小宽度\n    minHeight: 600, // 设置最小高度\n    autoHideMenuBar: true,\n    ...(process.platform === 'linux' ? { icon } : { icon }),\n    webPreferences: {\n      preload: join(__dirname, '../preload/index.js'),\n      nodeIntegration: false, // 禁用Node.js集成\n      contextIsolation: true // 启用上下文隔离\n    }\n  })\n\n  // 或者在窗口创建后设置\n  // mainWindow.setMinimumSize(800, 600)\n\n  mainWindow.on('ready-to-show', () => {\n    mainWindow.show()\n  })\n\n  mainWindow.webContents.setWindowOpenHandler((details) => {\n    shell.openExternal(details.url)\n    return { action: 'deny' }\n  })\n\n  // HMR for renderer base on electron-vite cli.\n  // Load the remote URL for development or the local html file for production.\n  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {\n    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])\n  } else {\n    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))\n  }\n}\n\n// This method will be called when Electron has finished\n// initialization and is ready to create browser windows.\n// Some APIs can only be used after this event occurs.\napp.whenReady().then(() => {\n  // Set app user model id for windows\n  electronApp.setAppUserModelId('com.electron')\n\n  // Default open or close DevTools by F12 in development\n  // and ignore CommandOrControl + R in production.\n  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils\n  app.on('browser-window-created', (_, window) => {\n    optimizer.watchWindowShortcuts(window)\n  })\n\n  // IPC test\n  ipcMain.on('ping', () => console.log('pong'))\n\n  createWindow()\n\n  app.on('activate', function () {\n    // On macOS it's common to re-create a window in the app when the\n    // dock icon is clicked and there are no other windows open.\n    if (BrowserWindow.getAllWindows().length === 0) createWindow()\n  })\n})\n\n// Quit when all windows are closed, except on macOS. There, it's common\n// for applications and their menu bar to stay active until the user quits\n// explicitly with Cmd + Q.\napp.on('window-all-closed', () => {\n  if (process.platform !== 'darwin') {\n    app.quit()\n  }\n})\n\n// In this file you can include the rest of your app's specific main process\n// code. You can also put them in separate files and require them here.\n"], "names": ["join", "BrowserWindow", "shell", "is", "app", "electronApp", "optimizer", "ipcMain"], "mappings": ";;;;AAEU,MAAA,OAAeA,KAAAA,KAAK,WAAW,0BAAoC;ACG7E,SAAS,eAAqB;AAE5B,QAAM,aAAa,IAAIC,uBAAc;AAAA,IACnC,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA;AAAA,IACV,WAAW;AAAA;AAAA,IACX,iBAAiB;AAAA,IACjB,GAAI,QAAQ,aAAa,UAAU,EAAE,KAAA,IAAS,EAAE,KAAA;AAAA,IAChD,gBAAgB;AAAA,MACd,SAASD,KAAAA,KAAK,WAAW,qBAAqB;AAAA,MAC9C,iBAAiB;AAAA;AAAA,MACjB,kBAAkB;AAAA;AAAA,IAAA;AAAA,EACpB,CACD;AAKD,aAAW,GAAG,iBAAiB,MAAM;AACnC,eAAW,KAAA;AAAA,EACb,CAAC;AAED,aAAW,YAAY,qBAAqB,CAAC,YAAY;AACvDE,mBAAM,aAAa,QAAQ,GAAG;AAC9B,WAAO,EAAE,QAAQ,OAAA;AAAA,EACnB,CAAC;AAID,MAAIC,MAAAA,GAAG,OAAO,QAAA,IAAY,uBAAuB,GAAG;AAClD,eAAW,QAAQ,QAAA,IAAY,uBAAuB,CAAC;AAAA,EACzD,OAAO;AACL,eAAW,SAASH,KAAAA,KAAK,WAAW,wBAAwB,CAAC;AAAA,EAC/D;AACF;AAKAI,SAAAA,IAAI,UAAA,EAAY,KAAK,MAAM;AAEzBC,QAAAA,YAAY,kBAAkB,cAAc;AAK5CD,WAAAA,IAAI,GAAG,0BAA0B,CAAC,GAAG,WAAW;AAC9CE,UAAAA,UAAU,qBAAqB,MAAM;AAAA,EACvC,CAAC;AAGDC,WAAAA,QAAQ,GAAG,QAAQ,MAAM,QAAQ,IAAI,MAAM,CAAC;AAE5C,eAAA;AAEAH,eAAI,GAAG,YAAY,WAAY;AAG7B,QAAIH,SAAAA,cAAc,cAAA,EAAgB,WAAW,EAAG,cAAA;AAAA,EAClD,CAAC;AACH,CAAC;AAKDG,SAAAA,IAAI,GAAG,qBAAqB,MAAM;AAChC,MAAI,QAAQ,aAAa,UAAU;AACjCA,aAAAA,IAAI,KAAA;AAAA,EACN;AACF,CAAC;"}